import { headers } from 'next/headers';

// Simple in-memory store. In a real-world scenario, you would use a more
// persistent and scalable solution like Redis or a database. This store
// will be reset on every server restart.
const requests = new Map<string, number[]>();
const LIMIT = 5; // 5 requests
const DURATION = 60 * 1000; // 1 minute in milliseconds

export class RateLimitError extends Error {
    constructor(message = 'Too many requests. Please try again in a minute.') {
        super(message);
        this.name = 'RateLimitError';
    }
}

export function checkRateLimit() {
  const ip = headers().get('x-forwarded-for') ?? '127.0.0.1';
  const userAgent = headers().get('user-agent') ?? 'unknown';
  const key = `${ip}-${userAgent}`;
  
  const now = Date.now();

  // Get timestamps for the current key, and filter out old ones
  const userTimestamps = (requests.get(key) ?? []).filter(
    (timestamp) => now - timestamp < DURATION
  );
  
  if (userTimestamps.length >= LIMIT) {
    throw new RateLimitError();
  }

  // Add current request timestamp and update the store
  userTimestamps.push(now);
  requests.set(key, userTimestamps);
}

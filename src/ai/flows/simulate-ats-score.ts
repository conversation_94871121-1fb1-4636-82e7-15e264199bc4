// This is an auto-generated file from Firebase Studio.

'use server';

/**
 * @fileOverview Simulates an Applicant Tracking System (ATS) score for a tailored resume based on a job description.
 *
 * - simulateATSScore - A function that takes a tailored resume and a job description as input and returns a simulated ATS score and justification.
 * - SimulateATSScoreInput - The input type for the simulateATSScore function.
 * - SimulateATSScoreOutput - The return type for the simulateATSScore function.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';
import { checkRateLimit } from '@/lib/rate-limiter';

const SimulateATSScoreInputSchema = z.object({
  tailoredResume: z.string().describe('The tailored resume to be scored.'),
  jobDescription: z.string().describe('The job description to compare the resume against.'),
  currentDate: z.string().describe('The current date to interpret date ranges like "2020 - Present".'),
});
export type SimulateATSScoreInput = z.infer<typeof SimulateATSScoreInputSchema>;

const SimulateATSScoreOutputSchema = z.object({
  atsScore: z.number().describe('The simulated ATS score (out of 100).'),
  justification: z.string().describe('A brief justification for the simulated ATS score.'),
});
export type SimulateATSScoreOutput = z.infer<typeof SimulateATSScoreOutputSchema>;

export async function simulateATSScore(input: SimulateATSScoreInput): Promise<SimulateATSScoreOutput> {
  await checkRateLimit();
  return simulateATSScoreFlow(input);
}

const prompt = ai.definePrompt({
  name: 'simulateATSScorePrompt',
  input: {schema: SimulateATSScoreInputSchema},
  output: {schema: SimulateATSScoreOutputSchema},
  prompt: `You are an expert in Applicant Tracking Systems (ATS) and resume analysis.

You will receive a tailored resume, a job description, and the current date. Your task is to simulate an ATS score (out of 100) for the resume based on its alignment with the job description. Use the current date to correctly interpret relative dates like "Present".

Current Date: {{{currentDate}}}

Provide a brief justification for the score, highlighting the key strengths and weaknesses of the resume in relation to the job description. Be concise and focus on the most important aspects.

Tailored Resume: {{{tailoredResume}}}
Job Description: {{{jobDescription}}}

Ensure that the ATS score is a number between 0 and 100, and the justification is clear and easy to understand.

Output the ATS score and justification as a JSON object.
`,
});

const simulateATSScoreFlow = ai.defineFlow(
  {
    name: 'simulateATSScoreFlow',
    inputSchema: SimulateATSScoreInputSchema,
    outputSchema: SimulateATSScoreOutputSchema,
  },
  async input => {
    const {output} = await prompt(input);
    return output!;
  }
);

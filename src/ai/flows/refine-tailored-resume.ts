'use server';
/**
 * @fileOverview Refines a tailored resume based on user feedback.
 *
 * - refineTailoredResume - A function that refines the tailored resume.
 * - RefineTailoredResumeInput - The input type for the refineTailoredResume function.
 * - RefineTailoredResumeOutput - The return type for the refineTailoredResume function.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';
import { checkRateLimit } from '@/lib/rate-limiter';

const RefineTailoredResumeInputSchema = z.object({
  originalResume: z.string().describe('The original resume content.'),
  jobDescription: z.string().describe('The job description content.'),
  tailoredResume: z.string().describe('The previously tailored resume content.'),
  feedback: z.string().describe('The user feedback or additional instructions.'),
  currentDate: z.string().describe('The current date to interpret date ranges like "2020 - Present".'),
});
export type RefineTailoredResumeInput = z.infer<typeof RefineTailoredResumeInputSchema>;

const RefineTailoredResumeOutputSchema = z.object({
  refinedResume: z.string().describe('The refined tailored resume content.'),
  atsScore: z.number().describe('The simulated ATS score (out of 100).'),
  scoreJustification: z.string().describe('The justification for the ATS score.'),
});
export type RefineTailoredResumeOutput = z.infer<typeof RefineTailoredResumeOutputSchema>;

export async function refineTailoredResume(input: RefineTailoredResumeInput): Promise<RefineTailoredResumeOutput> {
  checkRateLimit();
  return refineTailoredResumeFlow(input);
}

const refineTailoredResumePrompt = ai.definePrompt({
  name: 'refineTailoredResumePrompt',
  input: {schema: RefineTailoredResumeInputSchema},
  output: {schema: RefineTailoredResumeOutputSchema},
  prompt: `You are an expert resume writer specializing in tailoring resumes to specific job descriptions and Applicant Tracking Systems (ATS). You will take the original resume, job description, a previous tailored resume, user feedback, and the current date to create a refined resume, ATS score, and score justification. Use the current date to correctly interpret relative dates like "Present".

Current Date: {{{currentDate}}}
Original Resume: {{{originalResume}}}
Job Description: {{{jobDescription}}}
Tailored Resume: {{{tailoredResume}}}
Feedback: {{{feedback}}}

Based on the above information, refine the tailored resume to better match the job description. Also, provide a simulated ATS score (out of 100) and a brief justification for the score.

Output the refined resume, ATS score, and score justification as a JSON object.
`,
});

const refineTailoredResumeFlow = ai.defineFlow(
  {
    name: 'refineTailoredResumeFlow',
    inputSchema: RefineTailoredResumeInputSchema,
    outputSchema: RefineTailoredResumeOutputSchema,
  },
  async input => {
    const {output} = await refineTailoredResumePrompt(input);
    return output!;
  }
);

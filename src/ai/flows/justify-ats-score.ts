// src/ai/flows/justify-ats-score.ts
'use server';

/**
 * @fileOverview Generates a justification for the simulated ATS score, highlighting key strengths and weaknesses.
 *
 * - justifyATSScore - A function that generates the ATS score justification.
 * - JustifyATSScoreInput - The input type for the justifyATSScore function.
 * - JustifyATSScoreOutput - The return type for the justifyATSScore function.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';

const JustifyATSScoreInputSchema = z.object({
  resume: z.string().describe('The tailored resume text.'),
  jobDescription: z.string().describe('The job description text.'),
  atsScore: z.number().describe('The simulated ATS score (0-100).'),
  currentDate: z.string().describe('The current date to interpret date ranges like "2020 - Present".'),
});
export type JustifyATSScoreInput = z.infer<typeof JustifyATSScoreInputSchema>;

const JustifyATSScoreOutputSchema = z.object({
  justification: z.string().describe('A justification for the ATS score, highlighting strengths and weaknesses.'),
});
export type JustifyATSScoreOutput = z.infer<typeof JustifyATSScoreOutputSchema>;

export async function justifyATSScore(input: JustifyATSScoreInput): Promise<JustifyATSScoreOutput> {
  return justifyATSScoreFlow(input);
}

const prompt = ai.definePrompt({
  name: 'justifyATSScorePrompt',
  input: {schema: JustifyATSScoreInputSchema},
  output: {schema: JustifyATSScoreOutputSchema},
  prompt: `You are an expert career counselor specializing in Applicant Tracking Systems (ATS).
  You will receive a resume, job description, an ATS score, and the current date. Generate a brief justification for the ATS score, highlighting the resume's key strengths and weaknesses in relation to the job description. Provide actionable advice for improving the resume's alignment with the job description. Use the current date to correctly interpret relative dates like "Present".

  Current Date: {{{currentDate}}}

  Resume:
  {{{resume}}}

  Job Description:
  {{{jobDescription}}

  ATS Score:
  {{atsScore}}
  `,
});

const justifyATSScoreFlow = ai.defineFlow(
  {
    name: 'justifyATSScoreFlow',
    inputSchema: JustifyATSScoreInputSchema,
    outputSchema: JustifyATSScoreOutputSchema,
  },
  async input => {
    const {output} = await prompt(input);
    return output!;
  }
);

// tailor-resume.ts
'use server';
/**
 * @fileOverview This file defines a Genkit flow for tailoring a resume to a specific job description.
 *
 * - tailorResume - A function that accepts a resume and job description, and returns a tailored resume, an ATS score, and a justification for the score.
 * - TailorResumeInput - The input type for the tailorResume function.
 * - TailorResumeOutput - The return type for the tailorResume function.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';
import { checkRateLimit } from '@/lib/rate-limiter';

const TailorResumeInputSchema = z.object({
  resume: z.string().describe('The text of the original resume.'),
  jobDescription: z.string().describe('The text of the job description.'),
  currentDate: z.string().describe('The current date to interpret date ranges like "2020 - Present".'),
});
export type TailorResumeInput = z.infer<typeof TailorResumeInputSchema>;

const TailorResumeOutputSchema = z.object({
  tailoredResume: z.string().describe('The tailored resume text.'),
  atsScore: z.number().describe('A simulated ATS score (out of 100) for the tailored resume.'),
  scoreJustification: z.string().describe('A brief justification for the ATS score, highlighting key strengths and weaknesses.'),
});
export type TailorResumeOutput = z.infer<typeof TailorResumeOutputSchema>;

export async function tailorResume(input: TailorResumeInput): Promise<TailorResumeOutput> {
  await checkRateLimit();
  return tailorResumeFlow(input);
}

const tailorResumePrompt = ai.definePrompt({
  name: 'tailorResumePrompt',
  input: {schema: TailorResumeInputSchema},
  output: {schema: TailorResumeOutputSchema},
  prompt: `You are an expert resume tailor. You will receive a resume, a job description, and the current date.
Your task is to tailor the resume to the job description, highlighting the most relevant skills and experience. Use the current date to correctly interpret relative dates like "Present".

Current Date: {{{currentDate}}}

Here is the original resume:
{{{resume}}}

Here is the job description:
{{{jobDescription}}}

Generate a tailored resume that emphasizes the skills and experience most relevant to the job description.
Also, provide a simulated ATS score (out of 100) reflecting the resume's alignment with the job description and a brief justification for the score, highlighting key strengths and weaknesses.

Output the tailored resume, ATS score, and justification as a JSON object.`,
});

const tailorResumeFlow = ai.defineFlow(
  {
    name: 'tailorResumeFlow',
    inputSchema: TailorResumeInputSchema,
    outputSchema: TailorResumeOutputSchema,
  },
  async input => {
    const {output} = await tailorResumePrompt(input);
    return output!;
  }
);
